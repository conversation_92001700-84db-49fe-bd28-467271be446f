function switch_folder(folder)
{
    /*{{{*/
    var mat_righe = document.getElementsByTagName('table');
    for (cont in mat_righe)
    {
        try
        {
            att = mat_righe[cont].getAttribute("extra_tag");
            if (att.length > 0)
            {
                if (att == folder || (folder == 'tutti' && att != 'template'))
                {
                    mat_righe[cont].style.display = '';
                }
                else
                {
                    mat_righe[cont].style.display = 'none';
                }
            }
        }
        catch (e)
        {
        }
    }

    switch (folder)
    {
        /*{{{*/
        case 'ruoli':
            document.getElementById('bottone_folder_ruoli').disabled = true;
            document.getElementById('bottone_folder_verbali').disabled = false;
            document.getElementById('bottone_folder_stampe').disabled = false;
            document.getElementById('bottone_folder_registro').disabled = false;
            document.getElementById('bottone_folder_calendario').disabled = false;
            document.getElementById('bottone_folder_pubb').disabled = false;
            document.getElementById('bottone_folder_tutti').disabled = false;
            break;
        case 'verbali':
            document.getElementById('bottone_folder_ruoli').disabled = false;
            document.getElementById('bottone_folder_verbali').disabled = true;
            document.getElementById('bottone_folder_stampe').disabled = false;
            document.getElementById('bottone_folder_registro').disabled = false;
            document.getElementById('bottone_folder_calendario').disabled = false;
            document.getElementById('bottone_folder_pubb').disabled = false;
            document.getElementById('bottone_folder_tutti').disabled = false;
            break;
        case 'stampe':
            document.getElementById('bottone_folder_ruoli').disabled = false;
            document.getElementById('bottone_folder_verbali').disabled = false;
            document.getElementById('bottone_folder_stampe').disabled = true;
            document.getElementById('bottone_folder_registro').disabled = false;
            document.getElementById('bottone_folder_calendario').disabled = false;
            document.getElementById('bottone_folder_pubb').disabled = false;
            document.getElementById('bottone_folder_tutti').disabled = false;
            break;
        case 'registro':
            document.getElementById('bottone_folder_ruoli').disabled = false;
            document.getElementById('bottone_folder_verbali').disabled = false;
            document.getElementById('bottone_folder_stampe').disabled = false;
            document.getElementById('bottone_folder_registro').disabled = true;
            document.getElementById('bottone_folder_calendario').disabled = false;
            document.getElementById('bottone_folder_pubb').disabled = false;
            document.getElementById('bottone_folder_tutti').disabled = false;
            break;
        case 'calendario':
            document.getElementById('bottone_folder_ruoli').disabled = false;
            document.getElementById('bottone_folder_verbali').disabled = false;
            document.getElementById('bottone_folder_stampe').disabled = false;
            document.getElementById('bottone_folder_registro').disabled = false;
            document.getElementById('bottone_folder_calendario').disabled = true;
            document.getElementById('bottone_folder_pubb').disabled = false;
            document.getElementById('bottone_folder_tutti').disabled = false;
            break;
        case 'pubb':
            document.getElementById('bottone_folder_ruoli').disabled = false;
            document.getElementById('bottone_folder_verbali').disabled = false;
            document.getElementById('bottone_folder_stampe').disabled = false;
            document.getElementById('bottone_folder_registro').disabled = false;
            document.getElementById('bottone_folder_calendario').disabled = false;
            document.getElementById('bottone_folder_pubb').disabled = true;
            document.getElementById('bottone_folder_tutti').disabled = false;
            break;
        case 'tutti':
            document.getElementById('bottone_folder_ruoli').disabled = false;
            document.getElementById('bottone_folder_verbali').disabled = false;
            document.getElementById('bottone_folder_stampe').disabled = false;
            document.getElementById('bottone_folder_registro').disabled = false;
            document.getElementById('bottone_folder_calendario').disabled = false;
            document.getElementById('bottone_folder_pubb').disabled = false;
            document.getElementById('bottone_folder_tutti').disabled = true;
            break;
            /*}}}*/
    }
    /*}}}*/
}

function switch_folder_medie(folder_medie)
{
    /*{{{*/
    var mat_righe = document.getElementsByTagName('table');
    for (cont in mat_righe)
    {
        try
        {
            att = mat_righe[cont].getAttribute("extra_tag");
            if (att.length > 0)
            {
                if (att == folder_medie || (folder_medie == 'tutti' && att != 'template'))
                {
                    mat_righe[cont].style.display = '';
                }
                else
                {
                    mat_righe[cont].style.display = 'none';
                }
            }
        }
        catch (e)
        {
        }
    }

    switch (folder_medie)
    {
        /*{{{*/
        case 'ruoli':
            document.getElementById('bottone_folder_ruoli').disabled = true;
            document.getElementById('bottone_folder_verbali').disabled = false;
            document.getElementById('bottone_folder_stampe').disabled = false;
            document.getElementById('bottone_folder_registro').disabled = false;
            document.getElementById('bottone_folder_risultati').disabled = false;
            document.getElementById('bottone_folder_risultati_orali').disabled = false;
            document.getElementById('bottone_folder_risultati_finali').disabled = false;
            document.getElementById('bottone_folder_calendario_scritti').disabled = false;
            document.getElementById('bottone_folder_calendario_orali').disabled = false;
            document.getElementById('bottone_folder_scheda_raccordo').disabled = false;
            document.getElementById('bottone_folder_scheda_personale').disabled = false;
            document.getElementById('bottone_folder_pubb').disabled = false;
            document.getElementById('bottone_folder_tutti').disabled = false;
            document.getElementById('bottone_folder_esami_2021').disabled = false;
            break;
        case 'verbali':
            document.getElementById('bottone_folder_ruoli').disabled = false;
            document.getElementById('bottone_folder_verbali').disabled = true;
            document.getElementById('bottone_folder_stampe').disabled = false;
            document.getElementById('bottone_folder_registro').disabled = false;
            document.getElementById('bottone_folder_risultati').disabled = false;
            document.getElementById('bottone_folder_risultati_orali').disabled = false;
            document.getElementById('bottone_folder_risultati_finali').disabled = false;
            document.getElementById('bottone_folder_calendario_scritti').disabled = false;
            document.getElementById('bottone_folder_calendario_orali').disabled = false;
            document.getElementById('bottone_folder_scheda_raccordo').disabled = false;
            document.getElementById('bottone_folder_scheda_personale').disabled = false;
            document.getElementById('bottone_folder_pubb').disabled = false;
            document.getElementById('bottone_folder_tutti').disabled = false;
            document.getElementById('bottone_folder_esami_2021').disabled = false;
            break;
        case 'stampe':
            document.getElementById('bottone_folder_ruoli').disabled = false;
            document.getElementById('bottone_folder_verbali').disabled = false;
            document.getElementById('bottone_folder_stampe').disabled = true;
            document.getElementById('bottone_folder_registro').disabled = false;
            document.getElementById('bottone_folder_risultati').disabled = false;
            document.getElementById('bottone_folder_risultati_orali').disabled = false;
            document.getElementById('bottone_folder_risultati_finali').disabled = false;
            document.getElementById('bottone_folder_calendario_scritti').disabled = false;
            document.getElementById('bottone_folder_calendario_orali').disabled = false;
            document.getElementById('bottone_folder_scheda_raccordo').disabled = false;
            document.getElementById('bottone_folder_scheda_personale').disabled = false;
            document.getElementById('bottone_folder_pubb').disabled = false;
            document.getElementById('bottone_folder_tutti').disabled = false;
            document.getElementById('bottone_folder_esami_2021').disabled = false;
            break;
        case 'registro':
            document.getElementById('bottone_folder_ruoli').disabled = false;
            document.getElementById('bottone_folder_verbali').disabled = false;
            document.getElementById('bottone_folder_stampe').disabled = false;
            document.getElementById('bottone_folder_registro').disabled = true;
            document.getElementById('bottone_folder_risultati').disabled = false;
            document.getElementById('bottone_folder_risultati_orali').disabled = false;
            document.getElementById('bottone_folder_risultati_finali').disabled = false;
            document.getElementById('bottone_folder_calendario_scritti').disabled = false;
            document.getElementById('bottone_folder_calendario_orali').disabled = false;
            document.getElementById('bottone_folder_scheda_raccordo').disabled = false;
            document.getElementById('bottone_folder_scheda_personale').disabled = false;
            document.getElementById('bottone_folder_pubb').disabled = false;
            document.getElementById('bottone_folder_tutti').disabled = false;
            document.getElementById('bottone_folder_esami_2021').disabled = false;
            break;
        case 'risultati':
            document.getElementById('bottone_folder_ruoli').disabled = false;
            document.getElementById('bottone_folder_verbali').disabled = false;
            document.getElementById('bottone_folder_stampe').disabled = false;
            document.getElementById('bottone_folder_registro').disabled = false;
            document.getElementById('bottone_folder_risultati').disabled = true;
            document.getElementById('bottone_folder_risultati_orali').disabled = false;
            document.getElementById('bottone_folder_risultati_finali').disabled = false;
            document.getElementById('bottone_folder_calendario_scritti').disabled = false;
            document.getElementById('bottone_folder_calendario_orali').disabled = false;
            document.getElementById('bottone_folder_scheda_raccordo').disabled = false;
            document.getElementById('bottone_folder_scheda_personale').disabled = false;
            document.getElementById('bottone_folder_pubb').disabled = false;
            document.getElementById('bottone_folder_tutti').disabled = false;
            document.getElementById('bottone_folder_esami_2021').disabled = false;
            break;
        case 'risultati_orali':
            document.getElementById('bottone_folder_ruoli').disabled = false;
            document.getElementById('bottone_folder_verbali').disabled = false;
            document.getElementById('bottone_folder_stampe').disabled = false;
            document.getElementById('bottone_folder_registro').disabled = false;
            document.getElementById('bottone_folder_risultati').disabled = false;
            document.getElementById('bottone_folder_risultati_orali').disabled = true;
            document.getElementById('bottone_folder_risultati_finali').disabled = false;
            document.getElementById('bottone_folder_calendario_scritti').disabled = false;
            document.getElementById('bottone_folder_calendario_orali').disabled = false;
            document.getElementById('bottone_folder_scheda_raccordo').disabled = false;
            document.getElementById('bottone_folder_scheda_personale').disabled = false;
            document.getElementById('bottone_folder_pubb').disabled = false;
            document.getElementById('bottone_folder_tutti').disabled = false;
            document.getElementById('bottone_folder_esami_2021').disabled = false;
            break;
        case 'risultati_finali':
            document.getElementById('bottone_folder_ruoli').disabled = false;
            document.getElementById('bottone_folder_verbali').disabled = false;
            document.getElementById('bottone_folder_stampe').disabled = false;
            document.getElementById('bottone_folder_registro').disabled = false;
            document.getElementById('bottone_folder_risultati').disabled = false;
            document.getElementById('bottone_folder_risultati_orali').disabled = false;
            document.getElementById('bottone_folder_risultati_finali').disabled = true;
            document.getElementById('bottone_folder_calendario_scritti').disabled = false;
            document.getElementById('bottone_folder_calendario_orali').disabled = false;
            document.getElementById('bottone_folder_scheda_raccordo').disabled = false;
            document.getElementById('bottone_folder_scheda_personale').disabled = false;
            document.getElementById('bottone_folder_pubb').disabled = false;
            document.getElementById('bottone_folder_tutti').disabled = false;
            document.getElementById('bottone_folder_esami_2021').disabled = false;
            break;
        case 'calendario_scritti':
            document.getElementById('bottone_folder_ruoli').disabled = false;
            document.getElementById('bottone_folder_verbali').disabled = false;
            document.getElementById('bottone_folder_stampe').disabled = false;
            document.getElementById('bottone_folder_registro').disabled = false;
            document.getElementById('bottone_folder_risultati').disabled = false;
            document.getElementById('bottone_folder_risultati_orali').disabled = false;
            document.getElementById('bottone_folder_risultati_finali').disabled = false;
            document.getElementById('bottone_folder_calendario_scritti').disabled = true;
            document.getElementById('bottone_folder_calendario_orali').disabled = false;
            document.getElementById('bottone_folder_scheda_raccordo').disabled = false;
            document.getElementById('bottone_folder_scheda_personale').disabled = false;
            document.getElementById('bottone_folder_pubb').disabled = false;
            document.getElementById('bottone_folder_tutti').disabled = false;
            document.getElementById('bottone_folder_esami_2021').disabled = false;
            break;
        case 'calendario_orali':
            document.getElementById('bottone_folder_ruoli').disabled = false;
            document.getElementById('bottone_folder_verbali').disabled = false;
            document.getElementById('bottone_folder_stampe').disabled = false;
            document.getElementById('bottone_folder_registro').disabled = false;
            document.getElementById('bottone_folder_risultati').disabled = false;
            document.getElementById('bottone_folder_risultati_orali').disabled = false;
            document.getElementById('bottone_folder_risultati_finali').disabled = false;
            document.getElementById('bottone_folder_calendario_scritti').disabled = false;
            document.getElementById('bottone_folder_calendario_orali').disabled = true;
            document.getElementById('bottone_folder_scheda_raccordo').disabled = false;
            document.getElementById('bottone_folder_scheda_personale').disabled = false;
            document.getElementById('bottone_folder_pubb').disabled = false;
            document.getElementById('bottone_folder_tutti').disabled = false;
            document.getElementById('bottone_folder_esami_2021').disabled = false;
            break;
        case 'scheda_raccordo':
            document.getElementById('bottone_folder_ruoli').disabled = false;
            document.getElementById('bottone_folder_verbali').disabled = false;
            document.getElementById('bottone_folder_stampe').disabled = false;
            document.getElementById('bottone_folder_registro').disabled = false;
            document.getElementById('bottone_folder_risultati').disabled = false;
            document.getElementById('bottone_folder_risultati_orali').disabled = false;
            document.getElementById('bottone_folder_risultati_finali').disabled = false;
            document.getElementById('bottone_folder_calendario_scritti').disabled = false;
            document.getElementById('bottone_folder_calendario_orali').disabled = false;
            document.getElementById('bottone_folder_scheda_raccordo').disabled = true;
            document.getElementById('bottone_folder_scheda_personale').disabled = false;
            document.getElementById('bottone_folder_pubb').disabled = false;
            document.getElementById('bottone_folder_tutti').disabled = false;
            document.getElementById('bottone_folder_esami_2021').disabled = false;
            break;
        case 'scheda_personale':
            document.getElementById('bottone_folder_ruoli').disabled = false;
            document.getElementById('bottone_folder_verbali').disabled = false;
            document.getElementById('bottone_folder_stampe').disabled = false;
            document.getElementById('bottone_folder_registro').disabled = false;
            document.getElementById('bottone_folder_risultati').disabled = false;
            document.getElementById('bottone_folder_risultati_orali').disabled = false;
            document.getElementById('bottone_folder_risultati_finali').disabled = false;
            document.getElementById('bottone_folder_calendario_scritti').disabled = false;
            document.getElementById('bottone_folder_calendario_orali').disabled = false;
            document.getElementById('bottone_folder_scheda_raccordo').disabled = false;
            document.getElementById('bottone_folder_scheda_personale').disabled = true;
            document.getElementById('bottone_folder_pubb').disabled = false;
            document.getElementById('bottone_folder_tutti').disabled = false;
            document.getElementById('bottone_folder_esami_2021').disabled = false;
            break;
        case 'pubb':
            document.getElementById('bottone_folder_ruoli').disabled = false;
            document.getElementById('bottone_folder_verbali').disabled = false;
            document.getElementById('bottone_folder_stampe').disabled = false;
            document.getElementById('bottone_folder_registro').disabled = false;
            document.getElementById('bottone_folder_risultati').disabled = false;
            document.getElementById('bottone_folder_risultati_orali').disabled = false;
            document.getElementById('bottone_folder_risultati_finali').disabled = false;
            document.getElementById('bottone_folder_calendario_scritti').disabled = false;
            document.getElementById('bottone_folder_calendario_orali').disabled = false;
            document.getElementById('bottone_folder_scheda_raccordo').disabled = false;
            document.getElementById('bottone_folder_scheda_personale').disabled = false;
            document.getElementById('bottone_folder_pubb').disabled = true;
            document.getElementById('bottone_folder_tutti').disabled = false;
            document.getElementById('bottone_folder_esami_2021').disabled = false;
            break;
        case 'tutti':
            document.getElementById('bottone_folder_ruoli').disabled = false;
            document.getElementById('bottone_folder_verbali').disabled = false;
            document.getElementById('bottone_folder_stampe').disabled = false;
            document.getElementById('bottone_folder_registro').disabled = false;
            document.getElementById('bottone_folder_risultati').disabled = false;
            document.getElementById('bottone_folder_risultati_orali').disabled = false;
            document.getElementById('bottone_folder_risultati_finali').disabled = false;
            document.getElementById('bottone_folder_calendario_scritti').disabled = false;
            document.getElementById('bottone_folder_calendario_orali').disabled = false;
            document.getElementById('bottone_folder_scheda_raccordo').disabled = false;
            document.getElementById('bottone_folder_scheda_personale').disabled = false;
            document.getElementById('bottone_folder_pubb').disabled = false;
            document.getElementById('bottone_folder_tutti').disabled = true;
            document.getElementById('bottone_folder_esami_2021').disabled = false;
            break;

        case 'esami_2021':
            document.getElementById('bottone_folder_ruoli').disabled = false;
            document.getElementById('bottone_folder_verbali').disabled = false;
            document.getElementById('bottone_folder_stampe').disabled = false;
            document.getElementById('bottone_folder_registro').disabled = false;
            document.getElementById('bottone_folder_risultati').disabled = false;
            document.getElementById('bottone_folder_risultati_orali').disabled = false;
            document.getElementById('bottone_folder_risultati_finali').disabled = false;
            document.getElementById('bottone_folder_calendario_scritti').disabled = false;
            document.getElementById('bottone_folder_calendario_orali').disabled = false;
            document.getElementById('bottone_folder_scheda_raccordo').disabled = false;
            document.getElementById('bottone_folder_scheda_personale').disabled = false;
            document.getElementById('bottone_folder_pubb').disabled = false;
            document.getElementById('bottone_folder_tutti').disabled = false;
            document.getElementById('bottone_folder_esami_2021').disabled = true;
            break;
            /*}}}*/
    }
    /*}}}*/
}

function es_elimina(obj, tipo) {
    var desc="";
    var f_value="";
    switch(tipo) {
        case "verbali":
            desc="Eliminare il verbale selezionato?";
            f_value = obj.form.tipo_storico_verbale_stato.value;
            break;
        case "verbali_medie":
            desc="Eliminare il verbale selezionato?";
            f_value = obj.form.tipo_storico_verbale_stato_medie.value;
            break;
        case "scheda_personale":
            desc="Eliminare l\'elenco scheda personale del candidato selezionata?";
            f_value = obj.form.tipo_storico_modulo.value;
            break;
    }
    if (confirm(desc))
    {
        document.forms['form_reload'].stato_secondario.value = 'elimina_storico_documento';
        document.forms['form_reload'].form_folder.value = tipo;
        document.forms['form_reload'].form_id_storico_documento.value = f_value;
        document.forms['form_reload'].submit();
    }
}

function openPopupGiudizio(student, name, popup) {
    var field = $('[name="'+popup+'['+ student +']"]');
    var dialogElement = $('#pop_'+name+'_'+ student);

    // Distruggi il dialog se esiste già
    if (dialogElement.hasClass('ui-dialog-content')) {
        dialogElement.dialog('destroy');
    }

    dialogElement.dialog({
        width:540,
        position: { my: "center", at: "center", of: window },
        modal: true,
        buttons: {
            "Salva": function() {
                console.log($('[name="'+name+'_'+ student +'"]').val()+'->'+field.val());
                $('[name="'+name+'_'+ student +'"]').val(field.val());
                $(this).dialog("close");
                // Distruggi il dialog dopo la chiusura
                $(this).dialog("destroy");
                console.log($('[name="'+name+'_'+ student +'"]').val());
            },
            "Annulla": function() {
                $(this).dialog("close");
                // Distruggi il dialog dopo la chiusura
                $(this).dialog("destroy");
            }
        }
    });

    field.val($('[name="'+name+'_'+ student +'"]').val());
}
